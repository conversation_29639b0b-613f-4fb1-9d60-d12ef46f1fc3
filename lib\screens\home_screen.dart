import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/vpn_provider.dart';
import '../l10n/app_localizations.dart';
import 'privacy_policy_screen.dart';
import 'settings_screen.dart';
import '../widgets/ad_banner_widget.dart';
import '../widgets/server_list_bottom_sheet.dart';
import 'package:permission_handler/permission_handler.dart'; // Import permission_handler for opening settings

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  late AnimationController _iconAnimationController;
  late Animation<double> _iconRotationAnimation;

  // Helper to determine color based on connection state
  Color _getStatusColor(VpnConnectionState state, BuildContext context) {
    switch (state) {
      case VpnConnectionState.connected:
        return Colors.green;
      case VpnConnectionState.disconnected:
        return Colors.red;
      case VpnConnectionState.connecting:
      case VpnConnectionState.disconnecting:
        return Colors.orange;
      case VpnConnectionState.error:
        return Colors.deepOrange;
      case VpnConnectionState.permissionRequired: // Use a distinct color for permission needed
        return Colors.blueGrey;
      default:
        return Colors.grey;
    }
  }

  // Helper to get localized status text
  String _getLocalizedStatusText(VpnConnectionState state, String? serverName, AppLocalizations localizations, String? errorMessage) {
    final currentServerName = serverName ?? localizations.translate('selectServerTitle'); // Use translate for fallback
    switch (state) {
      case VpnConnectionState.connected:
        return localizations.connectedToServer(currentServerName);
      case VpnConnectionState.connecting:
        return localizations.connectingToServer(currentServerName);
      case VpnConnectionState.disconnected:
        return localizations.translate('statusDisconnected');
      case VpnConnectionState.disconnecting:
        return localizations.translate('statusDisconnecting');
      case VpnConnectionState.error:
        return "${localizations.translate('statusDisconnected')} (${localizations.statusErrorPlaceholder(null)})${errorMessage != null ? ': $errorMessage' : ''}"; // Include error message if available
      case VpnConnectionState.permissionRequired:
        return localizations.translate('permissionRequiredMessage'); // Use new key for message
      default:
        return localizations.translate('statusDisconnected');
    }
  }

  // Helper to get localized button text from key
  String _getLocalizedButtonText(String key, AppLocalizations localizations) {
    switch (key) {
      case 'disconnectButton': return localizations.translate('disconnectButton');
      case 'connectButton': return localizations.translate('connectButton');
      case 'statusConnecting': return localizations.translate('statusConnecting');
      case 'statusDisconnecting': return localizations.translate('statusDisconnecting');
      case 'statusErrorPlaceholder': return localizations.translate('statusErrorPlaceholder');
      case 'permissionRequiredButton': return localizations.translate('permissionRequiredButton'); // New key
      default: return localizations.translate('connectButton');
    }
  }


  @override
  void initState() {
    super.initState();
    _iconAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _iconRotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _iconAnimationController, curve: Curves.linear),
    );

    final vpnProvider = Provider.of<VpnProvider>(context, listen: false);
    vpnProvider.addListener(_vpnStateListener);

    _updateAnimation(vpnProvider.connectionState);

    // Trigger initial permission check
    // vpnProvider.checkVpnPermission(); // This is called in VpnProvider constructor
  }

  @override
  void dispose() {
    _iconAnimationController.dispose();
    Provider.of<VpnProvider>(context, listen: false).removeListener(_vpnStateListener);
    super.dispose();
  }

  void _vpnStateListener() {
    final currentState = Provider.of<VpnProvider>(context, listen: false).connectionState;
    _updateAnimation(currentState);
  }

  void _updateAnimation(VpnConnectionState state) {
    if (state == VpnConnectionState.connecting || state == VpnConnectionState.disconnecting) {
      if (!_iconAnimationController.isAnimating) {
        _iconAnimationController.repeat();
      }
    } else {
      if (_iconAnimationController.isAnimating) {
        _iconAnimationController.stop();
      }
    }
  }


  @override
  Widget build(BuildContext context) {
    final vpnProvider = Provider.of<VpnProvider>(context);
    final localizations = AppLocalizations.of(context)!;

    IconData currentIcon;
    switch (vpnProvider.connectionState) {
      case VpnConnectionState.connected:
        currentIcon = Icons.vpn_key;
        break;
      case VpnConnectionState.disconnected:
      case VpnConnectionState.error:
      case VpnConnectionState.permissionRequired: // Icon for permission required state
      default:
        currentIcon = Icons.vpn_key_off;
        break;
    }

    // Determine if the server selection button should be active
    final bool canSelectServer = vpnProvider.connectionState != VpnConnectionState.connecting &&
        vpnProvider.connectionState != VpnConnectionState.disconnecting &&
        vpnProvider.connectionState != VpnConnectionState.permissionRequired; // Cannot select server if permission is needed

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('appTitle')),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
        ],
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              // Status Icon with Animation inside a styled container
              Container(
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _getStatusColor(vpnProvider.connectionState, context).withOpacity(0.2),
                  border: Border.all(
                    color: _getStatusColor(vpnProvider.connectionState, context),
                    width: 3.0,
                  ),
                ),
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: RotationTransition(
                    turns: _iconRotationAnimation,
                    key: ValueKey<VpnConnectionState>(vpnProvider.connectionState),
                    child: Icon(
                      currentIcon,
                      size: 60,
                      color: _getStatusColor(vpnProvider.connectionState, context),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 30),

              // Status Text
              Text(
                _getLocalizedStatusText(vpnProvider.connectionState, vpnProvider.selectedServer?.name, localizations, vpnProvider.errorMessage),
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: _getStatusColor(vpnProvider.connectionState, context),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 15),

              // Connection Timer or Placeholder
              if (vpnProvider.connectionState == VpnConnectionState.connected)
                Text(
                  vpnProvider.formattedConnectionDuration,
                  style: const TextStyle(
                    fontSize: 30,
                    fontWeight: FontWeight.w800,
                    letterSpacing: 1.5,
                  ),
                )
              else
                const Text(
                  '--:--',
                  style: TextStyle(
                    fontSize: 30,
                    fontWeight: FontWeight.w800,
                    letterSpacing: 1.5,
                    color: Colors.grey,
                  ),
                ),

              const SizedBox(height: 50),

              // Connect/Disconnect/Permission Button
              ElevatedButton(
                onPressed: vpnProvider.connectButtonAction,
                style: Theme.of(context).elevatedButtonTheme.style?.copyWith( // Use theme style and override background color
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                      // Use status color for button background
                      return _getStatusColor(vpnProvider.connectionState, context);
                    },
                  ),
                  foregroundColor: MaterialStateProperty.all<Color>(Colors.white), // Keep text white
                  padding: MaterialStateProperty.all<EdgeInsetsGeometry>(
                    const EdgeInsets.symmetric(horizontal: 60, vertical: 18),
                  ),
                ),
                child: Text(
                  _getLocalizedButtonText(vpnProvider.connectButtonTextKey, localizations),
                  style: const TextStyle(fontSize: 20),
                ),
              ),
              const SizedBox(height: 20), // Slightly less space

              // If permission is permanently denied, show a link to app settings
              if (vpnProvider.connectionState == VpnConnectionState.permissionRequired &&
                  // You might need a flag in provider to indicate permanent denial,
                  // or check status using permission_handler here if relevant.
                  // For VpnService.prepare(), this check is less direct.
                  // We'll assume if state is permissionRequired, the button tries to request again.
                  // If requestVpnPermission shows the dialog, the user can deny permanently.
                  // A real app needs more sophisticated permission state tracking.
                  false // Placeholder: hide this button unless permanent denial is detected
              )
                TextButton(
                  onPressed: () {
                    openAppSettings(); // Opens app settings where user can grant permission
                  },
                  child: Text(localizations.translate('openAppSettings')), // Add this key
                ),
              const SizedBox(height: 5), // Small space

              // Server Selection Button
              TextButton(
                onPressed: canSelectServer ? () { // Only callable when canSelectServer is true
                  // Trigger server loading if not already loaded/loading
                  if (vpnProvider.availableServers.isEmpty && !vpnProvider.isLoadingServers) {
                    vpnProvider.fetchServersFromSource();
                  }
                  // Show bottom sheet
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.0),
                        topRight: Radius.circular(16.0),
                      ),
                    ),
                    builder: (context) {
                      // Pass localization object if needed in the sheet, or use Provider
                      return const ServerListBottomSheet();
                    },
                  );
                } : null, // Disable the button
                child: Text(
                  vpnProvider.selectedServer != null
                      ? '${localizations.translate('selectServerTitle')}: ${vpnProvider.selectedServer!.name}'
                      : localizations.translate('selectServerTitle'),
                  style: TextStyle(
                    fontSize: 18,
                    // Color based on state and enabled status
                    color: canSelectServer
                        ? Theme.of(context).colorScheme.primary
                        : Colors.grey,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(height: 30),

              // AdMob Banner
              const AdBannerWidget(),

              const Spacer(),

              // Privacy Policy Link
              TextButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()),
                  );
                },
                child: Text(localizations.translate('privacyPolicyTitle')),
              ),
            ],
          ),
        ),
      ),
    );
  }
}