import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:convert';
import 'package:permission_handler/permission_handler.dart'; // Import permission_handler
import '../l10n/app_localizations.dart'; // Import localization for messages

// Define possible VPN connection states
enum VpnConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
  error,
  permissionRequired, // New state for when permission is needed
}

// Simple model for a VPN server
class VpnServer {
  final String id;
  final String name;
  final String country;
  final String config; // Or path to config file/data
  final String protocol; // Added protocol field (e.g., 'openvpn', 'wireguard')

  VpnServer({
    required this.id,
    required this.name,
    required this.country,
    required this.config,
    required this.protocol, // Initialize protocol
  });

  // Example factory method to create from JSON
  factory VpnServer.fromJson(Map<String, dynamic> json) {
    return VpnServer(
      id: json['id'],
      name: json['name'],
      country: json['country'],
      config: json['config'],
      protocol: json['protocol'] ?? 'openvpn', // Default to openvpn if not specified
    );
  }

  // Helper to convert to JSON for platform channel
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'country': country,
      'config': config,
      'protocol': protocol, // Include protocol
    };
  }
}

class VpnProvider with ChangeNotifier {
  VpnConnectionState _connectionState = VpnConnectionState.disconnected;
  VpnServer? _selectedServer;
  List<VpnServer> _availableServers = [];
  bool _isLoadingServers = false; // New flag for server loading state

  // --- Platform Channel ---
  static const MethodChannel _channel = MethodChannel('com.your_app_name.vpn/vpn_service');
  // --- End Platform Channel ---

  // --- Timer Variables ---
  Timer? _timer;
  Duration _connectionDuration = Duration.zero;
  // --- End Timer Variables ---

  // --- Error Message ---
  String? _errorMessage; // To store specific error messages

  // --- Getters ---
  VpnConnectionState get connectionState => _connectionState;
  VpnServer? get selectedServer => _selectedServer;
  List<VpnServer> get availableServers => _availableServers;
  bool get isLoadingServers => _isLoadingServers;
  String? get errorMessage => _errorMessage; // Getter for error message

  // --- Timer Getters ---
  Duration get connectionDuration => _connectionDuration;

  String get formattedConnectionDuration {
    if (_connectionState == VpnConnectionState.connected) {
      String twoDigits(int n) => n.toString().padLeft(2, '0');
      final hours = twoDigits(_connectionDuration.inHours);
      final minutes = twoDigits(_connectionDuration.inMinutes.remainder(60));
      final seconds = twoDigits(_connectionDuration.inSeconds.remainder(60));
      if (_connectionDuration.inHours > 0) {
        return '$hours:$minutes:$seconds';
      } else {
        return '$minutes:$seconds';
      }
    } else {
      return '00:00'; // Show 00:00 when not connected
    }
  }
  // --- End Timer Getters ---


  VpnProvider() {
    // Do NOT load servers here in constructor if it's a potentially long operation.
    // Instead, trigger it when the server list screen/sheet is opened for the first time,
    // or when the app starts if you want to show them immediately.
    // _loadAvailableServers(); // Removed from constructor

    // Set up listeners for native events (e.g., connection state changes)
    _setupPlatformChannelListeners();
    // Check permission status on startup
    _checkVpnPermission();
  }

  // --- Permission Handling ---
  Future<bool> checkVpnPermission() async {
    // On Android, VPN permission is handled by VpnService.prepare() dialog,
    // which is triggered by the native code when you try to start VpnService.
    // PermissionHandler's Permission.vpn might not be universally supported
    // or needed depending on the native implementation method (VpnService vs root solution).
    // For VpnService on Android, the native code MUST handle the system dialog.
    // On iOS, NetworkExtension typically requires provisioning profiles/entitlements.
    // PermissionHandler can be used for other related permissions if needed (e.g., background).

    // This check is more conceptual for demonstrating the flow.
    // A real app might need to handle the Android VpnService.prepare() result specifically.
    print('Checking VPN permission...'); // Debug print
    // Placeholder: assume permission is 'granted' if not on Android for VpnService flow
    // For other permission types (like background), permission_handler is useful.
    // Let's use a dummy check or rely on the native side's result from VpnService.prepare().

    // **Native Code Responsibility:** When 'startVpn' is called on Android, the native code
    // should call VpnService.prepare(). If it returns null, permission is granted.
    // If it returns an Intent, launch that Intent to show the system dialog.
    // The result of that dialog should be sent back to Flutter via a Platform Channel callback.

    // For demonstration, let's simulate a check or assume the native side will handle the dialog.
    // If we needed a *generic* permission check (like background process), we'd use:
    // var status = await Permission.locationBackground.status; // Example

    // As VPN permission is special, we rely on the native system dialog flow.
    // However, we can add a state to indicate permission is *required* to prompt the user.

    // For a simple check, we might query the native side if the service is ready.
    try {
      // This is a hypothetical native method call
      final bool isPermissionGranted = await _channel.invokeMethod('isVpnPermissionGranted');
      print('isVpnPermissionGranted native call result: $isPermissionGranted');
      return isPermissionGranted;
    } on PlatformException catch (e) {
      print("Failed to check permission status from native: '${e.message}'. Assuming not granted.");
      return false; // Assume false on error
    } catch (e) {
      print('Unexpected error checking permission: $e');
      return false; // Assume false on other errors
    }
  }

  Future<void> requestVpnPermission() async {
    print('Requesting VPN permission...'); // Debug print
    // On Android, this usually means calling VpnService.prepare() on the native side.
    // We can trigger this from Flutter, but the dialog happens on the native side.
    try {
      // This is a hypothetical native method call to trigger the system dialog
      final bool permissionGranted = await _channel.invokeMethod('requestVpnPermission');
      print('Native requestVpnPermission result: $permissionGranted');

      // The native side should send a callback when the permission result is known
      // _updateStateFromString based on the result ("disconnected" if granted, "permissionRequired" if denied)

    } on PlatformException catch (e) {
      print("Failed to request permission from native: '${e.message}'.");
      // Handle errors, maybe set state to error or show message
      _connectionState = VpnConnectionState.error; // Or permissionRequired
      _errorMessage = e.message;
      notifyListeners();
    } catch (e) {
      print('Unexpected error requesting permission: $e');
      _connectionState = VpnConnectionState.error; // Or permissionRequired
      _errorMessage = e.toString();
      notifyListeners();
    }

    // Manual state update for simulation if native callbacks aren't fully implemented yet:
    // After calling the native request, you *hope* the native callback will update the state.
    // For testing, you might simulate the callback here after a delay.
    await Future.delayed(const Duration(seconds: 1)); // Simulate system dialog delay
    // Simulate permission granted:
    // _updateStateFromString('disconnected');
    // Simulate permission denied:
    // _connectionState = VpnConnectionState.permissionRequired;
    // notifyListeners();
  }

  Future<void> _checkVpnPermission() async {
    final isGranted = await checkVpnPermission();
    if (!isGranted && _connectionState != VpnConnectionState.connected) {
      _connectionState = VpnConnectionState.permissionRequired;
      notifyListeners();
      print('VPN permission required.'); // Debug print
    } else if (isGranted && _connectionState == VpnConnectionState.permissionRequired) {
      // Permission was granted externally (e.g., user went to settings)
      _connectionState = VpnConnectionState.disconnected; // Or previous state
      notifyListeners();
      print('VPN permission granted.'); // Debug print
    }
  }
  // --- End Permission Handling ---


  // --- Platform Channel Callbacks (Methods called from Native) ---
  Future<void> _handleNativeMethodCall(MethodCall call) async {
    print('Received native method call: ${call.method}'); // Debug print
    try {
      switch (call.method) {
        case 'vpnStateChanged':
          final stateString = call.arguments as String;
          _updateStateFromString(stateString);
          _errorMessage = null; // Clear error on state change
          break;
        case 'vpnError':
          final errorMessage = call.arguments as String;
          print('VPN Error from native: $errorMessage');
          _connectionState = VpnConnectionState.error;
          _errorMessage = errorMessage; // Store the error message
          _stopTimer();
          notifyListeners();
          // TODO: Show error message to user (e.g., using a dialog)
          break;
        case 'permissionStatus':
        // Native code reports permission status (e.g., after VpnService.prepare())
          final bool isGranted = call.arguments as bool;
          if (isGranted) {
            _connectionState = VpnConnectionState.disconnected; // Ready to connect
          } else {
            _connectionState = VpnConnectionState.permissionRequired; // Permission denied
            _errorMessage = null; // Clear previous error
          }
          notifyListeners();
          print('Permission status received from native: $isGranted'); // Debug print
          break;
        case 'stopVpnFromNotification':
          print('Stop VPN requested from notification.'); // Debug print
          // This callback is triggered when the user taps the stop button on the notification.
          // We should initiate the disconnect process from here.
          await disconnect();
          break;
      // Add other cases for traffic stats, connection progress, etc.
        default:
          print('Ignoring unknown native method: ${call.method}');
      }
    } on PlatformException catch (e) {
      print('Error handling native method call via PlatformException: ${call.method} - ${e.message}');
      _connectionState = VpnConnectionState.error;
      _errorMessage = e.message;
      _stopTimer();
      notifyListeners();
    } catch (e) {
      print('Error handling native method call: ${call.method} - $e');
      _connectionState = VpnConnectionState.error;
      _errorMessage = e.toString();
      _stopTimer();
      notifyListeners();
    }
  }

  void _setupPlatformChannelListeners() {
    _channel.setMethodCallHandler(_handleNativeMethodCall);
    print('Platform Channel listener setup.');
  }

  void _updateStateFromString(String state) {
    print('Native state update received: $state');
    VpnConnectionState newState;
    switch(state.toLowerCase()) {
      case 'connecting': newState = VpnConnectionState.connecting; break;
      case 'connected': newState = VpnConnectionState.connected; break;
      case 'disconnecting': newState = VpnConnectionState.disconnecting; break;
      case 'disconnected': newState = VpnConnectionState.disconnected; break;
      case 'error': newState = VpnConnectionState.error; break;
      default: newState = VpnConnectionState.disconnected;
    }

    if (_connectionState != newState) {
      _connectionState = newState;
      if (newState == VpnConnectionState.connected) {
        _startTimer();
        // Native code MUST call the method below when connected and started foreground service
        // _channel.invokeMethod('showForegroundNotification', {'status': 'connected', 'server': _selectedServer!.name}); // Example data
      } else if (newState == VpnConnectionState.disconnected || newState == VpnConnectionState.error) {
        _stopTimer();
        // Native code MUST call the method below when disconnected and service is stopping
        // _channel.invokeMethod('hideForegroundNotification'); // Example
      } else if (newState == VpnConnectionState.connecting) {
        // Native code MUST call the method below when connecting and service is starting
        // _channel.invokeMethod('showForegroundNotification', {'status': 'connecting', 'server': _selectedServer!.name}); // Example data
      }
      notifyListeners();
    }
  }
  // --- End Platform Channel Callbacks ---


  // --- Timer Management ---
  void _startTimer() {
    print('Starting timer...');
    _connectionDuration = Duration.zero;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _connectionDuration = _connectionDuration + const Duration(seconds: 1);
      notifyListeners();
    });
  }

  void _stopTimer() {
    print('Stopping timer...');
    _timer?.cancel();
    _timer = null;
    _connectionDuration = Duration.zero; // Reset duration when stopped
    notifyListeners();
  }

  @override
  void dispose() {
    _stopTimer();
    // Consider removing the method call handler if VpnProvider's lifecycle allows it
    // _channel.setMethodCallHandler(null); // Use with caution
    super.dispose();
  }
  // --- End Timer Management ---


  // --- App Logic Methods ---

  Future<void> fetchServersFromSource() async {
    if (_isLoadingServers || _availableServers.isNotEmpty) {
      if (!_isLoadingServers && _availableServers.isNotEmpty) {
        notifyListeners();
      }
      return;
    }

    _isLoadingServers = true;
    _availableServers = [];
    _selectedServer = null;
    notifyListeners();

    try {
      // --- TODO: Implement real server fetching logic here ---
      // This could be an HTTP request to an API, reading a local file, etc.
      print('Fetching servers...');
      await Future.delayed(const Duration(seconds: 2)); // Simulate network delay

      // Example: Loading from a JSON string or API response
      final List<Map<String, dynamic>> dummyServerData = [
        {'id': 'us_1', 'name': 'USA - New York', 'country': 'US', 'config': '...ovpn config US...', 'protocol': 'openvpn'},
        {'id': 'ca_1', 'name': 'Canada - Toronto', 'country': 'CA', 'config': '...ovpn config CA...', 'protocol': 'openvpn'},
        {'id': 'uk_1', 'name': 'UK - London', 'country': 'GB', 'config': '...ovpn config UK...', 'protocol': 'wireguard'}, // Example WireGuard
        {'id': 'de_1', 'name': 'Germany - Frankfurt', 'country': 'DE', 'config': '...ovpn config DE...', 'protocol': 'openvpn'},
        {'id': 'sa_1', 'name': 'Saudi Arabia - Riyadh', 'country': 'SA', 'config': '...ovpn config SA...', 'protocol': 'openvpn'},
      ];
      _availableServers = dummyServerData.map((json) => VpnServer.fromJson(json)).toList();
      // --- End TODO ---

      if (_availableServers.isNotEmpty) {
        _selectedServer = _availableServers.first;
      }

      print('Servers fetched successfully. Count: ${_availableServers.length}');

    } catch (e) {
      print('Error fetching servers: $e');
      _errorMessage = 'Failed to load servers: ${e.toString()}'; // Store server loading error
      notifyListeners();
      // TODO: Handle error (e.g., show an error message to the user)
    } finally {
      _isLoadingServers = false;
      notifyListeners();
    }
  }


  void selectServer(VpnServer server) {
    if (_selectedServer?.id != server.id) {
      _selectedServer = server;
      // If connected, consider prompting user to reconnect to the new server
      notifyListeners();
      print('Server selected: ${server.name}');
    }
  }

  Future<void> connect() async {
    if (_connectionState != VpnConnectionState.disconnected && _connectionState != VpnConnectionState.permissionRequired) {
      print('Connect called when not disconnected or permission required. Current state: $_connectionState');
      return;
    }
    if (_connectionState == VpnConnectionState.permissionRequired) {
      print('Connect called but permission is required. Requesting permission...');
      await requestVpnPermission(); // Request permission first
      // Wait for native callback 'permissionStatus' to update state
      return; // Exit for now, native callback will trigger connect if granted
    }
    if (_selectedServer == null) {
      print('Connect called but no server selected.');
      // TODO: Show a message to the user asking them to select a server
      _errorMessage = 'Please select a server to connect.'; // Example error message
      notifyListeners();
      return;
    }
    _errorMessage = null; // Clear any previous error
    _connectionState = VpnConnectionState.connecting;
    notifyListeners();
    print('Connection initiated to ${_selectedServer!.name} using protocol ${_selectedServer!.protocol}...');

    try {
      // --- Call Native Code to Start VPN ---
      // Native MUST implement a method handler for 'startVpn'
      // It receives server data (config, protocol, etc.)
      // It MUST handle VpnService.prepare() dialog on Android.
      // It MUST use a native OpenVPN/WireGuard library to establish the connection.
      // It MUST run a foreground service and show a persistent notification.
      // It MUST report connection state changes and errors back to Flutter via _channel.invokeMethod.

      final result = await _channel.invokeMethod('startVpn', {
        'server': _selectedServer!.toJson(), // Pass selected server data (includes config and protocol)
        // 'protocol': _selectedServer!.protocol, // Redundant if included in server JSON
        // Pass other necessary info like user credentials if any
      });
      print('Native startVpn method call result: $result');

      // State updates and timer handled by native callbacks ('vpnStateChanged', 'vpnError')

    } on PlatformException catch (e) {
      print("PlatformException calling startVpn: '${e.message}'.");
      _connectionState = VpnConnectionState.error;
      _errorMessage = e.message;
      _stopTimer();
      notifyListeners();
      // TODO: Show error message to user
    } catch (e) {
      print('Unexpected error calling startVpn: $e');
      _connectionState = VpnConnectionState.error;
      _errorMessage = e.toString();
      _stopTimer();
      notifyListeners();
      // TODO: Show error message to user
    }
  }

  Future<void> disconnect() async {
    if (_connectionState != VpnConnectionState.connected && _connectionState != VpnConnectionState.error) { // Allow disconnecting from error state too?
      print('Disconnect called when not connected or error. Current state: $_connectionState');
      return;
    }

    _connectionState = VpnConnectionState.disconnecting;
    notifyListeners();
    print('Disconnection initiated...');
    _errorMessage = null; // Clear error on disconnect attempt

    try {
      // --- Call Native Code to Stop VPN ---
      // Native MUST implement a method handler for 'stopVpn'
      // It MUST stop the native VPN service and hide the notification.
      // It MUST report state changes back to Flutter.
      final result = await _channel.invokeMethod('stopVpn');
      print('Native stopVpn method call result: $result');

      _stopTimer(); // Stop timer immediately when disconnection is initiated

      // State updates handled by native callbacks ('vpnStateChanged', 'vpnError')

    } on PlatformException catch (e) {
      print("PlatformException calling stopVpn: '${e.message}'.");
      _connectionState = VpnConnectionState.error;
      _errorMessage = e.message;
      // The timer is already stopped above
      notifyListeners();
      // TODO: Show error message to user
    } catch (e) {
      print('Unexpected error calling stopVpn: $e');
      _connectionState = VpnConnectionState.error;
      _errorMessage = e.toString();
      // The timer is already stopped above
      notifyListeners();
      // TODO: Show error message to user
    }
  }

  // Helper to get button text key based on state
  String get connectButtonTextKey {
    switch (_connectionState) {
      case VpnConnectionState.connected: return 'disconnectButton';
      case VpnConnectionState.disconnected: return 'connectButton';
      case VpnConnectionState.connecting: return 'statusConnecting';
      case VpnConnectionState.disconnecting: return 'statusDisconnecting';
      case VpnConnectionState.error: return 'statusErrorPlaceholder';
      case VpnConnectionState.permissionRequired: return 'permissionRequiredButton'; // New key
      default: return 'connectButton';
    }
  }

  // Helper to get button action based on state
  VoidCallback? get connectButtonAction {
    switch (_connectionState) {
      case VpnConnectionState.connected:
        return disconnect;
      case VpnConnectionState.disconnected:
      // Only allow connecting if a server is selected
        return _selectedServer != null ? connect : null;
      case VpnConnectionState.connecting:
      case VpnConnectionState.disconnecting:
      case VpnConnectionState.error:
        return disconnect; // Allow attempting to disconnect from error state
      case VpnConnectionState.permissionRequired:
        return requestVpnPermission; // Button requests permission
      default:
        return null;
    }
  }

  // Helper to get status text key based on state
  String get statusTextKey {
    switch (_connectionState) {
      case VpnConnectionState.connected: return 'connected';
      case VpnConnectionState.connecting: return 'connecting';
      case VpnConnectionState.disconnected: return 'disconnected';
      case VpnConnectionState.disconnecting: return 'disconnecting';
      case VpnConnectionState.error: return 'error';
      case VpnConnectionState.permissionRequired: return 'permissionRequired'; // New key
      default: return 'disconnected';
    }
  }
}