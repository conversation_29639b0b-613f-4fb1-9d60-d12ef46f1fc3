import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:convert';
import 'package:permission_handler/permission_handler.dart';
import 'package:openvpn_flutter/openvpn_flutter.dart';
import '../l10n/app_localizations.dart';

// Define possible VPN connection states
enum VpnConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
  error,
  permissionRequired,
}

// Simple model for a VPN server
class VpnServer {
  final String id;
  final String name;
  final String country;
  final String config; // OpenVPN config content
  final String protocol;

  VpnServer({
    required this.id,
    required this.name,
    required this.country,
    required this.config,
    required this.protocol,
  });

  factory VpnServer.fromJson(Map<String, dynamic> json) {
    return VpnServer(
      id: json['id'],
      name: json['name'],
      country: json['country'],
      config: json['config'],
      protocol: json['protocol'] ?? 'openvpn',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'country': country,
      'config': config,
      'protocol': protocol,
    };
  }
}

class VpnProvider with ChangeNotifier {
  VpnConnectionState _connectionState = VpnConnectionState.disconnected;
  VpnServer? _selectedServer;
  List<VpnServer> _availableServers = [];
  bool _isLoadingServers = false;

  // OpenVPN Flutter Plugin
  late OpenVPN _openVPN;

  // Timer Variables
  Timer? _timer;
  Duration _connectionDuration = Duration.zero;

  // Error Message
  String? _errorMessage;

  // Getters
  VpnConnectionState get connectionState => _connectionState;
  VpnServer? get selectedServer => _selectedServer;
  List<VpnServer> get availableServers => _availableServers;
  bool get isLoadingServers => _isLoadingServers;
  String? get errorMessage => _errorMessage;
  Duration get connectionDuration => _connectionDuration;

  String get formattedConnectionDuration {
    if (_connectionState == VpnConnectionState.connected) {
      String twoDigits(int n) => n.toString().padLeft(2, '0');
      final hours = twoDigits(_connectionDuration.inHours);
      final minutes = twoDigits(_connectionDuration.inMinutes.remainder(60));
      final seconds = twoDigits(_connectionDuration.inSeconds.remainder(60));
      if (_connectionDuration.inHours > 0) {
        return '$hours:$minutes:$seconds';
      } else {
        return '$minutes:$seconds';
      }
    } else {
      return '00:00';
    }
  }

  VpnProvider() {
    _initializeOpenVPN();
  }

  void _initializeOpenVPN() {
    _openVPN = OpenVPN(
      onVpnStatusChanged: _onVpnStatusChanged,
      onVpnStageChanged: _onVpnStageChanged,
    );
    
    // Initialize the OpenVPN plugin
    _openVPN.initialize(
      groupIdentifier: "group.com.example.vpn", // For iOS
      providerBundleIdentifier: "com.example.vpn.VPNExtension", // For iOS
      localizedDescription: "VPN Connection", // For iOS
    );
  }

  void _onVpnStatusChanged(VPNStatus? status) {
    print('VPN Status changed: $status');
    // Handle status changes if needed
  }

  void _onVpnStageChanged(VPNStage? stage) {
    print('VPN Stage changed: $stage');
    
    VpnConnectionState newState;
    switch (stage) {
      case VPNStage.connecting:
        newState = VpnConnectionState.connecting;
        break;
      case VPNStage.connected:
        newState = VpnConnectionState.connected;
        break;
      case VPNStage.disconnecting:
        newState = VpnConnectionState.disconnecting;
        break;
      case VPNStage.disconnected:
        newState = VpnConnectionState.disconnected;
        break;
      case VPNStage.error:
        newState = VpnConnectionState.error;
        break;
      default:
        newState = VpnConnectionState.disconnected;
    }

    if (_connectionState != newState) {
      _connectionState = newState;
      _errorMessage = null; // Clear error on state change
      
      if (newState == VpnConnectionState.connected) {
        _startTimer();
      } else if (newState == VpnConnectionState.disconnected || 
                 newState == VpnConnectionState.error) {
        _stopTimer();
      }
      
      notifyListeners();
    }
  }

  // Timer Management
  void _startTimer() {
    print('Starting timer...');
    _connectionDuration = Duration.zero;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _connectionDuration = _connectionDuration + const Duration(seconds: 1);
      notifyListeners();
    });
  }

  void _stopTimer() {
    print('Stopping timer...');
    _timer?.cancel();
    _timer = null;
    _connectionDuration = Duration.zero;
    notifyListeners();
  }

  @override
  void dispose() {
    _stopTimer();
    super.dispose();
  }

  // Server Management
  Future<void> fetchServersFromSource() async {
    if (_isLoadingServers || _availableServers.isNotEmpty) {
      if (!_isLoadingServers && _availableServers.isNotEmpty) {
        notifyListeners();
      }
      return;
    }

    _isLoadingServers = true;
    _availableServers = [];
    _selectedServer = null;
    notifyListeners();

    try {
      print('Fetching servers...');
      await Future.delayed(const Duration(seconds: 2));

      // Example server data - replace with real server configs
      final List<Map<String, dynamic>> dummyServerData = [
        {
          'id': 'us_1',
          'name': 'USA - New York',
          'country': 'US',
          'config': '''client
dev tun
proto udp
remote your-server.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
verb 3''',
          'protocol': 'openvpn'
        },
        {
          'id': 'ca_1',
          'name': 'Canada - Toronto',
          'country': 'CA',
          'config': '''client
dev tun
proto udp
remote ca-server.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
verb 3''',
          'protocol': 'openvpn'
        },
      ];
      
      _availableServers = dummyServerData.map((json) => VpnServer.fromJson(json)).toList();

      if (_availableServers.isNotEmpty) {
        _selectedServer = _availableServers.first;
      }

      print('Servers fetched successfully. Count: ${_availableServers.length}');
    } catch (e) {
      print('Error fetching servers: $e');
      _errorMessage = 'Failed to load servers: ${e.toString()}';
      notifyListeners();
    } finally {
      _isLoadingServers = false;
      notifyListeners();
    }
  }

  void selectServer(VpnServer server) {
    if (_selectedServer?.id != server.id) {
      _selectedServer = server;
      notifyListeners();
      print('Server selected: ${server.name}');
    }
  }

  // VPN Connection Methods
  Future<void> connect() async {
    if (_connectionState != VpnConnectionState.disconnected && 
        _connectionState != VpnConnectionState.permissionRequired) {
      print('Connect called when not disconnected. Current state: $_connectionState');
      return;
    }

    if (_selectedServer == null) {
      print('Connect called but no server selected.');
      _errorMessage = 'Please select a server to connect.';
      notifyListeners();
      return;
    }

    _errorMessage = null;
    _connectionState = VpnConnectionState.connecting;
    notifyListeners();
    print('Connection initiated to ${_selectedServer!.name}...');

    try {
      await _openVPN.connect(
        _selectedServer!.config,
        _selectedServer!.name,
        certIsRequired: false,
      );
    } catch (e) {
      print('Error connecting to VPN: $e');
      _connectionState = VpnConnectionState.error;
      _errorMessage = e.toString();
      _stopTimer();
      notifyListeners();
    }
  }

  Future<void> disconnect() async {
    if (_connectionState != VpnConnectionState.connected && 
        _connectionState != VpnConnectionState.error) {
      print('Disconnect called when not connected. Current state: $_connectionState');
      return;
    }

    _connectionState = VpnConnectionState.disconnecting;
    notifyListeners();
    print('Disconnection initiated...');
    _errorMessage = null;

    try {
      await _openVPN.disconnect();
      _stopTimer();
    } catch (e) {
      print('Error disconnecting VPN: $e');
      _connectionState = VpnConnectionState.error;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Helper to get button text key based on state
  String get connectButtonTextKey {
    switch (_connectionState) {
      case VpnConnectionState.connected:
        return 'disconnectButton';
      case VpnConnectionState.disconnected:
        return 'connectButton';
      case VpnConnectionState.connecting:
        return 'statusConnecting';
      case VpnConnectionState.disconnecting:
        return 'statusDisconnecting';
      case VpnConnectionState.error:
        return 'statusErrorPlaceholder';
      case VpnConnectionState.permissionRequired:
        return 'permissionRequiredButton';
      default:
        return 'connectButton';
    }
  }

  // Helper to get button action based on state
  VoidCallback? get connectButtonAction {
    switch (_connectionState) {
      case VpnConnectionState.disconnected:
      case VpnConnectionState.permissionRequired:
        return connect;
      case VpnConnectionState.connected:
      case VpnConnectionState.error:
        return disconnect;
      case VpnConnectionState.connecting:
      case VpnConnectionState.disconnecting:
        return null; // Disable button during transitions
      default:
        return null;
    }
  }
}
