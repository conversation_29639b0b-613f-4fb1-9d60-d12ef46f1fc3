import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../l10n/app_localizations.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('settingsTitle')),
      ),
      body: ListView(
        children: [
          // Theme Mode Setting
          ListTile(
            title: Text(localizations.translate('themeSetting')),
            trailing: DropdownButton<ThemeMode>(
              value: settingsProvider.themeMode,
              onChanged: (ThemeMode? newValue) {
                if (newValue != null) {
                  settingsProvider.setThemeMode(newValue);
                }
              },
              items: [
                DropdownMenuItem(
                  value: ThemeMode.system,
                  child: Text(localizations.translate('systemMode')),
                ),
                DropdownMenuItem(
                  value: ThemeMode.light,
                  child: Text(localizations.translate('lightMode')),
                ),
                DropdownMenuItem(
                  value: ThemeMode.dark,
                  child: Text(localizations.translate('darkMode')),
                ),
              ],
            ),
          ),
          // Language Setting
          ListTile(
            title: Text(localizations.translate('languageSetting')),
            trailing: DropdownButton<Locale?>(
              value: settingsProvider.locale,
              onChanged: (Locale? newValue) {
                settingsProvider.setLocale(newValue);
              },
              items: [
                DropdownMenuItem(
                  value: null, // System default
                  child: Text(localizations.translate('systemMode')), // Can reuse 'System Default' text
                ),
                DropdownMenuItem(
                  value: const Locale('en'),
                  child: Text('English'),
                ),
                DropdownMenuItem(
                  value: const Locale('ar'),
                  child: Text('العربية'),
                ),
              ],
            ),
          ),
          // Add other settings here
        ],
      ),
    );
  }
}