import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
// import 'l10n/app_localizations.dart'; // Generated by l10n.yaml - Commented out if using custom one
import 'package:google_mobile_ads/google_mobile_ads.dart'; // Import AdMob

import 'l10n/app_localizations.dart'; // Use your custom AppLocalizations

import 'providers/vpn_provider.dart';
import 'providers/settings_provider.dart';
import 'screens/home_screen.dart';

void main() {
  // Initialize AdMob
  WidgetsFlutterBinding.ensureInitialized();
  MobileAds.instance.initialize();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ChangeNotifierProvider(create: (_) => VpnProvider()),
        // Add other providers here
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Listen to settings provider for theme and locale changes
    final settingsProvider = Provider.of<SettingsProvider>(context);

    return MaterialApp(
      title: 'Secure VPN',
      debugShowCheckedModeBanner: false, // Set to false for release
      // --- Localization Setup ---
      supportedLocales: const [
        Locale('en', ''), // English
        Locale('ar', ''), // Arabic
      ],
      localizationsDelegates: const [
        // AppLocalizations.delegate, // Generated delegate - Comment out if using custom
        AppLocalizations.delegate, // Use your custom delegate
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      locale: settingsProvider.locale, // Use locale from settings provider
      // --- End Localization Setup ---

      // --- Theme Setup ---
      theme: ThemeData(
        brightness: Brightness.light,
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        appBarTheme: const AppBarTheme( // Made const
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 18),
              elevation: 5.0,
            )
        ),
        textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
            )
        ),
        listTileTheme: const ListTileThemeData( // Made const
          contentPadding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        ),
      ),
      darkTheme: ThemeData(
        brightness: Brightness.dark,
        primarySwatch: Colors.blueGrey,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        appBarTheme: const AppBarTheme( // Made const
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
        ),
        scaffoldBackgroundColor: Colors.grey[900],
        cardColor: Colors.grey[800],
        textTheme: const TextTheme( // Made const
          bodyMedium: TextStyle(color: Colors.white70),
          titleMedium: TextStyle(color: Colors.white),
          headlineSmall: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 18),
              elevation: 5.0,
            )
        ),
        textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
            )
        ),
        listTileTheme: ListTileThemeData(
          textColor: Colors.white70,
          iconColor: Colors.white70,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          tileColor: Colors.grey[800],
          selectedTileColor: Colors.blueGrey.shade700,
          selectedColor: Colors.green,
        ),
      ),
      themeMode: settingsProvider.themeMode,
      home: const HomeScreen(),
    );
  }
}