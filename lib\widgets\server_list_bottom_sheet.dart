import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/vpn_provider.dart';
import '../l10n/app_localizations.dart';

class ServerListBottomSheet extends StatelessWidget {
  const ServerListBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final vpnProvider = Provider.of<VpnProvider>(context);
    final localizations = AppLocalizations.of(context)!;

    return Container(
      height: MediaQuery.of(context).size.height * 0.6, // Take 60% of screen height
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor, // Match scaffold background
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              localizations.translate('selectServerTitle'), // Use translate method
              style: Theme.of(context).textTheme.headlineSmall, // Use headline style
            ),
          ),
          Expanded(
            // Show loading indicator or list
            child: vpnProvider.isLoadingServers
                ? const Center(child: CircularProgressIndicator())
                : vpnProvider.availableServers.isEmpty
                ? Center(child: Text(localizations.translate('noServersAvailable'))) // Use translate for the new key
                : ListView.builder(
              itemCount: vpnProvider.availableServers.length,
              itemBuilder: (context, index) {
                final server = vpnProvider.availableServers[index];
                final isSelected = vpnProvider.selectedServer?.id == server.id;
                // Disable tiles if connecting/disconnecting
                final bool isEnabled = vpnProvider.connectionState != VpnConnectionState.connecting &&
                    vpnProvider.connectionState != VpnConnectionState.disconnecting;

                return ListTile(
                  leading: Icon(Icons.public), // Placeholder for flags
                  title: Text(server.name),
                  trailing: isSelected ? const Icon(Icons.check, color: Colors.green) : null,
                  onTap: isEnabled ? () {
                    vpnProvider.selectServer(server);
                    Navigator.pop(context); // Close the bottom sheet
                  } : null, // Disable onTap when not enabled
                  // Add styling to indicate disabled state if needed
                  enabled: isEnabled,
                  tileColor: isEnabled ? Theme.of(context).listTileTheme.tileColor : Theme.of(context).disabledColor.withOpacity(0.1),
                  textColor: isEnabled ? Theme.of(context).listTileTheme.textColor : Theme.of(context).disabledColor,
                  iconColor: isEnabled ? Theme.of(context).listTileTheme.iconColor : Theme.of(context).disabledColor,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}