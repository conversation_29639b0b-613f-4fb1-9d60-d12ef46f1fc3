import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/vpn_provider.dart';
import '../l10n/app_localizations.dart';

class ServerListScreen extends StatelessWidget {
  const ServerListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final vpnProvider = Provider.of<VpnProvider>(context);
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('selectServerTitle')),
      ),
      body: vpnProvider.availableServers.isEmpty
          ? Center(child: CircularProgressIndicator()) // Show loading if servers not loaded
          : ListView.builder(
        itemCount: vpnProvider.availableServers.length,
        itemBuilder: (context, index) {
          final server = vpnProvider.availableServers[index];
          final isSelected = vpnProvider.selectedServer?.id == server.id;
          return ListTile(
            leading: Icon(Icons.public), // Replace with flag icons later if needed
            title: Text(server.name),
            trailing: isSelected ? Icon(Icons.check, color: Colors.green) : null,
            onTap: () {
              vpnProvider.selectServer(server);
              Navigator.pop(context); // Go back after selection
            },
          );
        },
      ),
    );
  }
}