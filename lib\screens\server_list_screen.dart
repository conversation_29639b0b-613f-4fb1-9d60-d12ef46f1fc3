import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/vpn_provider.dart';
import '../l10n/app_localizations.dart';

class ServerListBottomSheet extends StatelessWidget {
  const ServerListBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final vpnProvider = Provider.of<VpnProvider>(context);
    final localizations = AppLocalizations.of(context)!;

    // Determine if tiles should be enabled
    final bool tilesEnabled = vpnProvider.connectionState != VpnConnectionState.connecting &&
        vpnProvider.connectionState != VpnConnectionState.disconnecting &&
        vpnProvider.connectionState != VpnConnectionState.permissionRequired;

    return Container(
      height: MediaQuery.of(context).size.height * 0.6, // Take 60% of screen height
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor, // Match scaffold background
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              localizations.translate('selectServerTitle'),
              style: Theme.of(context).textTheme.headlineSmall,
            ),
          ),
          Expanded(
            child: vpnProvider.isLoadingServers
                ? const Center(child: CircularProgressIndicator())
                : vpnProvider.availableServers.isEmpty
                ? Center(child: Text(localizations.translate('noServersAvailable')))
                : ListView.builder(
              itemCount: vpnProvider.availableServers.length,
              itemBuilder: (context, index) {
                final server = vpnProvider.availableServers[index];
                final isSelected = vpnProvider.selectedServer?.id == server.id;

                return ListTile(
                  leading: Icon(Icons.public), // Placeholder for flags
                  title: Text('${server.name} (${server.protocol.toUpperCase()})'), // Show protocol
                  trailing: isSelected ? const Icon(Icons.check, color: Colors.green) : null,
                  onTap: tilesEnabled ? () {
                    vpnProvider.selectServer(server);
                    Navigator.pop(context);
                  } : null, // Disable onTap when not enabled
                  enabled: tilesEnabled,
                  tileColor: tilesEnabled ? Theme.of(context).listTileTheme.tileColor : Theme.of(context).disabledColor.withOpacity(0.1),
                  textColor: tilesEnabled ? Theme.of(context).listTileTheme.textColor : Theme.of(context).disabledColor,
                  iconColor: tilesEnabled ? Theme.of(context).listTileTheme.iconColor : Theme.of(context).disabledColor,
                  selectedTileColor: Theme.of(context).listTileTheme.selectedTileColor, // Apply selected color theme
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}