import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'dart:io' show Platform;

class AdBannerWidget extends StatefulWidget {
  const AdBannerWidget({super.key});

  @override
  State<AdBannerWidget> createState() => _AdBannerWidgetState();
}

class _AdBannerWidgetState extends State<AdBannerWidget> {
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;

  // TEST MODE CONFIGURATION
  // These are Google's official test ad unit IDs
  // IMPORTANT: Replace with your real ad unit IDs before publishing!
  static const String _testAndroidBannerAdUnitId =
      'ca-app-pub-3940256099942555/6300978111';
  static const String _testIOSBannerAdUnitId =
      'ca-app-pub-3940256099942555/2934735716';

  // Set to true for development/testing, false for production
  static const bool _isTestMode = false;

  String get adUnitId {
    if (_isTestMode) {
      return Platform.isAndroid
          ? _testAndroidBannerAdUnitId
          : _testIOSBannerAdUnitId;
    } else {
      // TODO: Replace with your actual production ad unit IDs
      return Platform.isAndroid
          ? _testAndroidBannerAdUnitId
          : _testIOSBannerAdUnitId;
    }
  }

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  void _loadBannerAd() {
    print('Loading banner ad in ${_isTestMode ? "TEST" : "PRODUCTION"} mode');
    print('Ad Unit ID: $adUnitId');

    _bannerAd = BannerAd(
      adUnitId: adUnitId,
      request: const AdRequest(),
      size: AdSize.banner,
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          setState(() {
            _isAdLoaded = true;
          });
          print(
              '✅ Banner ad loaded successfully in ${_isTestMode ? "TEST" : "PRODUCTION"} mode');
        },
        onAdFailedToLoad: (ad, err) {
          ad.dispose();
          setState(() {
            _isAdLoaded = false;
          });
          print('❌ Banner ad failed to load: ${err.message}');
          print('Error code: ${err.code}');
          if (_isTestMode) {
            print(
                '💡 This is expected in test mode if you\'re not connected to internet');
          }
        },
        onAdOpened: (ad) {
          print('📱 Banner ad opened');
        },
        onAdClosed: (ad) {
          print('📱 Banner ad closed');
        },
        onAdImpression: (ad) {
          print('👁️ Banner ad impression recorded');
        },
      ),
    )..load();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isAdLoaded && _bannerAd != null) {
      return Container(
        alignment: Alignment.center,
        width: _bannerAd!.size.width.toDouble(),
        height: _bannerAd!.size.height.toDouble(),
        child: AdWidget(ad: _bannerAd!),
      );
    } else {
      // Optional: Return an empty SizedBox or a placeholder
      return SizedBox(
        width: AdSize.banner.width.toDouble(),
        height: AdSize.banner.height.toDouble(),
        // child: Center(child: Text('Ad Placeholder')), // Optional placeholder text
      );
    }
  }
}
