import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'dart:io' show Platform;

class AdBannerWidget extends StatefulWidget {
  const AdBannerWidget({super.key});

  @override
  State<AdBannerWidget> createState() => _AdBannerWidgetState();
}

class _AdBannerWidgetState extends State<AdBannerWidget> {
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;

  // TODO: Replace with your actual Ad Unit IDs!
  // You can use test IDs during development:
  // Android: ca-app-pub-3940256099942555/6300978111
  // iOS: ca-app-pub-3940256099942555/2934735716
  final String adUnitId = Platform.isAndroid
      ? 'ca-app-pub-3940256099942555/6300978111' // Android Test ID
      : 'ca-app-pub-3940256099942555/2934735716'; // iOS Test ID

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  void _loadBannerAd() {
    _bannerAd = BannerAd(
      adUnitId: adUnitId,
      request: const AdRequest(),
      size: AdSize.banner,
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          setState(() {
            _isAdLoaded = true;
          });
          print('Banner ad loaded.'); // Debug print
        },
        onAdFailedToLoad: (ad, err) {
          ad.dispose();
          _isAdLoaded = false; // Ensure flag is false on failure
          print('Banner ad failed to load: ${err.message}'); // Debug print
        },
        // Add other listeners like onAdOpened, onAdClosed, onAdImpression
      ),
    )..load();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isAdLoaded && _bannerAd != null) {
      return Container(
        alignment: Alignment.center,
        width: _bannerAd!.size.width.toDouble(),
        height: _bannerAd!.size.height.toDouble(),
        child: AdWidget(ad: _bannerAd!),
      );
    } else {
      // Optional: Return an empty SizedBox or a placeholder
      return SizedBox(
        width: AdSize.banner.width.toDouble(),
        height: AdSize.banner.height.toDouble(),
        // child: Center(child: Text('Ad Placeholder')), // Optional placeholder text
      );
    }
  }
}