import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // Add support for statusErrorPlaceholder and parameterized strings
  // Note: Assuming the placeholder itself isn't passed into statusErrorPlaceholder method in UI code,
  // as the ARB file just defines the 'Error' string. If it were dynamic, the ARB structure
  // would be different, e.g., "statusErrorPlaceholder": "Error: {details}".
  String statusErrorPlaceholder(String? details) {
    String baseError = _localizedValues[locale.languageCode]?["statusErrorPlaceholder"] ?? "Error";
    // If details were needed, you'd handle them here, e.g., return "$baseError: $details"
    return baseError;
  }


  String connectingToServer(String serverName) {
    String template = _localizedValues[locale.languageCode]?["connectingToServer"] ?? "Connecting to {serverName}...";
    return template.replaceAll('{serverName}', serverName);
  }

  String connectedToServer(String serverName) {
    String template = _localizedValues[locale.languageCode]?["connectedToServer"] ?? "Connected to {serverName}";
    return template.replaceAll('{serverName}', serverName);
  }


  // --- Existing Localized Values (keep all existing keys) ---
  static const _localizedValues = <String, Map<String, String>>{
    'en': {
      "appTitle": "Secure VPN",
      "connectButton": "Connect",
      "disconnectButton": "Disconnect",
      "statusConnected": "Connected",
      "statusDisconnected": "Disconnected",
      "statusConnecting": "Connecting...",
      "statusDisconnecting": "Disconnecting...",
      "selectServerTitle": "Select Server",
      "privacyPolicyTitle": "Privacy Policy",
      "settingsTitle": "Settings",
      "themeSetting": "Theme Mode",
      "languageSetting": "Language",
      "darkMode": "Dark Mode",
      "lightMode": "Light Mode",
      "systemMode": "System Default",
      "connectingToServer": "Connecting to {serverName}...",
      "connectedToServer": "Connected to {serverName}",
      "statusErrorPlaceholder": "Error" // Added this key
    },
    'ar': {
      "appTitle": "شبكة افتراضية آمنة",
      "connectButton": "اتصال",
      "disconnectButton": "قطع الاتصال",
      "statusConnected": "متصل",
      "statusDisconnected": "غير متصل",
      "statusConnecting": "جارٍ الاتصال...",
      "statusDisconnecting": "جارٍ قطع الاتصال...",
      "selectServerTitle": "اختر السيرفر",
      "privacyPolicyTitle": "سياسة الخصوصية",
      "settingsTitle": "الإعدادات",
      "themeSetting": "وضع الثيم",
      "languageSetting": "اللغة",
      "darkMode": "الوضع الليلي",
      "lightMode": "الوضع النهاري",
      "systemMode": "وضع النظام الافتراضي",
      "connectingToServer": "جارٍ الاتصال بالسيرفر {serverName}...",
      "connectedToServer": "متصل بالسيرفر {serverName}",
      "statusErrorPlaceholder": "خطأ" // Added this key
    },
  };
  // --- End Existing Localized Values ---


  // Translate simple keys
  String translate(String key) {
    return _localizedValues[locale.languageCode]?[key] ?? key;
  }


  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => ['en', 'ar'].contains(locale.languageCode);

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(LocalizationsDelegate<AppLocalizations> old) => false;
}